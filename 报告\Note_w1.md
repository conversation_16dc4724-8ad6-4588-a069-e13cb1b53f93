## Xterminal
172.1.0.154 主要测试服务器
172.1.0.86 网页端，连接154

## 模型输出格式更改
### Prompt Engineering
对小模型稳定性较低，容易不符合格式
https://zhuanlan.zhihu.com/p/679120808
CO-STAR framework
- **C**ontext
- **O**bjective: 明确目标任务
- **S**tyle
- **T**one: 回答态度
- **A**udience: 回答的对象
- **R**esponse: 提供 LLM 回答的格式

{  
    "restriction": "给出类似<format>格式的JSON回答",  
    "text": 生成 10 个人名，要引用古籍,  
    "format": "data": {
        "Names": [
            {
                "name": ,
                "allusion": 
            }
        ]
    }
}

#### 分隔符和XML标签
用分隔符和XML来明确输入内容，让模型更容易理解意图

### json_repair

### Constrained Decoding 约束解码
稳定性高，开销大
#### XGrammar

