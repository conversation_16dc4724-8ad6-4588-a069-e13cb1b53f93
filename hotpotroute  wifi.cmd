@REM --route delete 0.0.0.0 mask 0.0.0.0
@REM --route add 0.0.0.0 mask 0.0.0.0 ************
@REM --route add 0.0.0.0 mask 0.0.0.0 ***********
@REM --route add ********* mask *********** ************
@REM --route add *********** mask ************* ************
@REM --route add *********** mask ************* ************
@REM --route add ********** mask ************* ************
@REM --route add ************ mask *************** ************

@REM --route add 0.0.0.0 mask 0.0.0.0 ************


--本地网段删除
route delete 0.0.0.0 mask 0.0.0.0
--wifi网段路由对应的wifi网关其他所有ip转到wifi网关请求外网（***********换为wifi网关）
route add 0.0.0.0 mask 0.0.0.0 ***********
--内网网段路由的内网网关172.1.0开头的ip转到以太网网关（************换为内网网关）
route add ********* mask ************* ************
--公司其他内网网段路由到内网关网段
route add ************ mask *************** ************