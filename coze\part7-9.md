# 7. 会话管理
## 7.1 创建会话
### 描述：用于创建会话
### 输入:
- **conversationName**(String) 会话名称
### 输出：
- isSucess(Boolean) 创建是否成功
- isExisted(Boolean) 会话是否已存在
- conversationId(String) 会话ID

## 7.2 修改会话
### 描述：用于修改会话的名字
### 输入:
- **conversationName**(String) 会话名称
- **newConversationName**(String) 新会话名称
### 输出：
- isSucess(Boolean) 修改是否成功
- isExisted(Boolean) 会话是否已存在
- conversationId(String) 会话ID

## 7.3 删除会话
### 描述：用于删除会话
### 输入：
- **conversationName**(String) 会话名称
### 输出：
- isSucess(Boolean) 删除是否成功

## 7.4 查询会话列表
### 描述：用于查询所有会话，包含静态会话、动态会话
### 输入
### 输出：
- conversationList(Array<$Object$>)
    + conversationName(String)
    + conversationId(String)
  

# 8. 会话历史
## 8.1 查询会话历史
### 描述：用于查询会话历史，返回LLM可见的会话消息
### 输入：
- **conversationName**(String)
- **rounds**(Integer)
### 输出：
- messageList(Array<$Object$>)
    - role(String)
    - content(String)

## 8.2 清空会话历史
### 描述：用于清空会话历史，清空后LLM看到的会话历史为空
### 输入：
- **conversationName**(String)
### 输出：
- isSuccess(Boolean) 清空是否成功


# 9. 消息
## 9.1 创建消息
### 描述：用于创建消息
### 输入：
- **conversationName**(String) 会话名称
- **role**(String) 消息角色
- **content**(String) 消息内容
### 输出：
- isSuccess(Boolean) 创建是否成功
- message(Object)
    - messageId(String) 消息ID

## 9.2 修改消息
### 描述：用于修改消息的内容
### 输入：
- **conversationName**(String) 会话名称
- **messageId**(String) 消息ID
- **newContent**(String) 新消息内容
### 输出：
- isSuccess(Boolean) 修改是否成功

## 9.3 删除消息
### 描述：用于删除消息
### 输入：
- **conversationName**(String) 会话名称
- **messageId**(String) 消息ID
### 输出：
- isSuccess(Boolean) 删除是否成功

## 9.4 查询消息列表
### 描述：用于查询消息列表
### 输入：
- **conversationName**(String) 会话名称
- limit(Integer) 每次查询返回的数据量
- beforeId(String) 查看指定位置之前的消息。传入空字符串，表示不指定位置。如需向前翻页，则指定为返回结果中的 first_id。
- afterId(String) 查看指定位置之后的消息。传入空字符串，表示不指定位置。如需向后翻页，则指定为返回结果中的 last_id。
### 输出：
- messageList(Array<$Object$>)
    + messageId(String) 消息ID
    + role(String) 消息角色
    + contentType(String) 
    + content(String) 
- firstId(String) 
- lastId(String) 
- hasMore(Boolean) 
