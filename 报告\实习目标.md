# 实习目标
产品部-算法组  
康行健

## 实习想获得什么

- 掌握智能体开发的基本原理和主流框架（如OpenAI Gym、RLlib等）。
- 提升强化学习、深度学习等相关算法的理解与实际应用能力。
- 参与实际项目，积累智能体开发的工程经验。
- 学习团队协作和项目管理流程，提升沟通与协作能力。

## 三个月总体实习目标

- 独立完成一个基于强化学习的智能体开发任务，包括需求分析、算法设计、模型训练与评估。
- 能够阅读并理解主流智能体相关论文，并将部分创新点应用到实际项目中。
- 掌握常用的智能体开发工具链，具备基本的代码优化和调试能力。


## 每月目标

**第一个月：**
- 熟悉公司业务与团队开发流程，了解项目背景。
- 学习并掌握智能体开发的基础知识与常用工具。
- 改进现有模块，辅助新功能开发

**第二个月：**
- 参与实际项目开发，承担部分子模块的设计与实现。
- 与团队成员协作，定期汇报进展，解决开发中遇到的问题。

**第三个月：**
- 独立负责一个小型智能体项目的开发与优化。
- 对项目进行性能评估和结果分析，撰写技术文档。