## 工作流-大模型模块

### 输出栏增加格式选项
下拉栏形式，支持选择不同的输出格式：常规输出、JSON、markdown
#### json格式
可通过图像界面新增变量，
![alt text](image.png)
也可转换为代码界面输入
example_input:

    {
        "data": {  
            "name": "example_name",  
            "age": "example_age",  
            "work": ["example_job1", "example_job2"]  
        }  
    }

### 输出显示
#### 将json格式输出以代码块形式展示
example_output:

    ```json\n{\n   \"data\": {\n       \"name\": \"张三\",\n       \"age\": \"35\",\n       \"work\": [\"程序员\", \"网约车司机\", \"外卖配送员\"]\n   }\n}\n```

需要将\n等字符正常显示

#### markdown格式增加预览

