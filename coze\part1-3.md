# 1.
## 1.1 大模型
### 描述：调用大语言模型,使用变量和提示词生成回复
### 模型：
- 热门模型（推荐几个常用的模型）
    - 豆包 1.5 Pro 32k
    - 豆包 1.5 Pro 深度思考 128k
    - 豆包 1.5 Pro 视觉推理 128k
    - DeepSeek-R1/250528
    - DeepSeek-V3-0324
- 全部模型
    - 豆包（工具调用/Pro/Lite/视觉理解/通用模型/角色扮演）
    - DeepSeek（R1/V3/Distill/工具调用）
    - 通义千问 Max
    - Kimi(8k/32k/128k)
    - 百川 4
    - 阶跃星辰（1.5v视频理解/1v图像理解）
    - 智谱 4
    - MiniMax abab6.5s
- 技能：（工具/插件）同下1.2插件

- 输入：输入需要添加到提示词的信息，这些信息可以被下方的提示词引用
- 视觉理解输入：传入图片or视频的url，并在Prompt中引用该输入，举例：“图片{{变量名}}中有什么？”
- 系统提示词：（系统级指导，设定人设和回复逻辑等）可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引入输入参数的变量
- 用户提示词：（向模型提供用户指令，如查询或任何基于用户指令的提问）可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引入输入参数的变量
- 输出：
    - 输出格式：文本、Markdown、JSON（默认）
    - 变量（自定）
  
## 1.2 插件
- 链接读取：获取网页、pdf、doc、docx、xlsx、csv、text 内容
- 图片理解：回答用户关于代表URL的图片的问题
- 头条搜索：使用头条的搜索功能来阅读或搜索URL链接，由于个别网站自身站点限制，无法获取网页内容
- 文生图
- 思维导图
- 天气
- 地图
- PPT
- ...

## 1.3 工作流
- 资源库工作流
- 官方示例
    - summarize_article：文章总结
    - image_cover：生成封面
    - condition：意图识别
    - PPT_generate：PPT一键生成，输入主题和受众，输出思路和和大纲，以及PPT模板的下载链接
    - split_messages：把较长文本拆分多个，拟人发消息
    - libai：大模型模仿李白

# 2. 业务逻辑
## 2.1 代码
### 输入：
自定义变量输入
### 代码：
浏览器内嵌仿现代IDE黑色主题
### 输出：
自定义变量输出
### 异常处理
- 超时时间（s）
- 重试次数：不重试/重试1次
- 异常处理方式：中断流程/返回设定内容/执行异常流程

## 2.2 选择器
- 变量1：模块输入/引用{用户变量/应用变量/系统变量}
- 判断条件：等于大于小于/长度判断/包含不包含/为空不为空（条件随输入变量类型变化）
- 变量2：可以直接写在模块，也可以同变量1一样输入

## 2.3 意图识别
### 模式：
极速模式/完整模式（调用大模型）
### 输入：
- query：输入或引用参数
### 意图匹配：
需要添加多个可能的意图
### 输出：
- classificationId(Integer)
### 异常处理

## 2.4 循环
### 循环设置
使用数组/指定循环次数/无限循环
### 循环数组
在使用数组时，需要指定循环的数组变量
### 中间变量
可选
### 输出
自定义变量输出
### 循环体（在插入循环后自动在下方链接一个循环体子流程）
额外可用流程：设置变量/终止循环/继续循环

## 2.5 批处理
### 描述：通过设定批量运行次数和逻辑，运行批处理体内的任务
### 循环设置
- 并行运行数量
- 批处理次数上限
### 输入
Array变量：批处理输入的数组变量
### 输出
自定义变量输出
### 批处理体（在插入批处理后自动在下方链接一个子流程体）
子流程体

## 2.6 变量聚合
### 描述：对多个分支的输出进行聚合处理
### 聚合策略：
返回每个分组中第一个非空的值（目前仅支持一种聚合模式）
### 输入：
Group内可包含多个输入或引用参数
### 输出：
每个Group一个输出变量

# 3. 输入输出
## 3.1 输入
### 输入：
自定义变量输入

## 3.2 输出：
自定义变量输出
### 输出：
自定义变量输出
### 输出内容：
- 流式输出开关
- 可以使用{{变量名}}、{{变量名.子变量名}}、{{变量名[数组索引]}}的方式引入输入参数的变量